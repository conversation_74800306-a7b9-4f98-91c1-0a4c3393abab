
@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Lora', serif;
    line-height: 1.6;
    color: #fff8ef;
    background-color: #29303b;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
}


h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #fbd19a;
    margin: 20px 0 12px 0;
    line-height: 1.3;
}

h1 {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbc99a !important;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0 auto 12px auto;
    max-width: 800px;
}

h2 {
    font-size: 28px;
    color: #fbd19a;
    margin: 20px 0 12px 0;
    text-align: center;
}

h3 {
    font-size: 18px;
    color: #fbd19a;
    margin: 16px 0 8px 0;
}

h4 {
    font-size: 16px;
    color: #fbd19a;
    margin: 12px 0 8px 0;
}




.banner {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.banner h2 {
    color: #fbd19a;
    font-size: 2.5rem;
    z-index: 1;
    position: relative;
    text-shadow:
        0 0 10px rgba(4, 7, 15, 0.9),
        0 0 20px rgba(4, 7, 15, 0.7),
        2px 2px 4px rgba(4, 7, 15, 0.8);
    margin: 0;
}

.banner .einheitsbutton {
    z-index: 1;
    position: relative;
}


.banner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1;
    position: relative;
}

.banner-zwischenmenschlichkeit {
    background-image: url('assets/bilder/zwischenmenschlichkeit.png');
    background-size: cover !important;
    background-position: center 30% !important;
}


.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.scroll-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.arrow-symbol {
    color: #fbc99a;
    font-size: 28px;
    font-weight: 300;
    line-height: 0.6;
    transform: rotate(-90deg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.arrow-symbol:first-child {
    margin-bottom: -8px;
    opacity: 0.7;
}

.arrow-symbol:last-child {
    opacity: 1;
}

.scroll-arrow:hover .arrow-symbol {
    color: #f59034;
}

.scroll-arrow:hover {
    transform: scale(1.1);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}


@media (min-width: 769px) {
    .banner-zwischenmenschlichkeit {
        background-position: center 45% !important;
    }
}

.banner-zusammenhalt {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-fuehrung {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-blumenfeld {
    background-image: url('assets/bilder/blumenfeld.png');
}

.banner-weekend {
    background-image: url('assets/bilder/weekend.png');
}

.banner-zukunftinsjetzt {
    background-image: url('assets/bilder/zukunftinsjetzt.png');
}

/* Hero Section Styles */
.hero-section {
    min-height: 100vh;
    background-color: #fff8ef;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.hero-brand {
    position: absolute;
    top: 20px;
    left: 30px;
    font-family: 'Montserrat', sans-serif;
    font-size: 24px;
    font-weight: 300;
    color: #fbc99a;
    letter-spacing: 1px;
    text-transform: uppercase;
    z-index: 10;
    height: 33px;
    line-height: 33px;
}

.hero-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    height: 100vh;
    margin: 0;
    padding: 0;
    gap: 0;
}

.hero-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 40px;
    height: 100%;
}

.hero-title {
    font-family: 'Montserrat', sans-serif;
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 700;
    color: #29303b;
    text-transform: uppercase;
    letter-spacing: 1px;
    line-height: 1.2;
    margin: 0 0 20px 0;
    white-space: pre-line;
}

.hero-subtitle {
    font-family: 'Montserrat', sans-serif;
    font-size: clamp(1rem, 2vw, 1.5rem);
    font-weight: 300;
    color: #b44355;
    letter-spacing: 3px;
    text-transform: uppercase;
    margin: 0 0 40px 0;
}

.hero-image {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
}

.hero-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-section .scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.hero-section .scroll-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-section .arrow-symbol {
    color: #fbc99a;
    font-size: 28px;
    font-weight: 300;
    line-height: 0.6;
    transform: rotate(-90deg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section .scroll-arrow:hover .arrow-symbol {
    color: #f59034;
}

.hero-section .button-container {
    margin-top: 0;
}

.banner-event {
    background-image: url('assets/bilder/frauen.jpg');
}

.banner-jana {
    background-image: url('assets/bilder/steg.jpg');
}


@media (min-width: 769px) {

    .banner-zukunftinsjetzt {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-zukunftinsjetzt::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }








    .banner-zukunftinsjetzt::after {
        content: "CO-KREATION FÜR LIGHTWORKER";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: clamp(1rem, 3vw, 1.8rem);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 40px);
        text-align: center;
        padding: 0 20px;
        letter-spacing: clamp(0.5px, 0.3vw, 2px);
        text-shadow:
            0 0 8px rgba(4, 7, 15, 0.8),
            0 0 15px rgba(4, 7, 15, 0.6),
            1px 1px 3px rgba(4, 7, 15, 0.9);
        box-sizing: border-box;
        display: block !important;
    }


    .banner-event {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-event::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }





    .banner-jana {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-jana::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }


    .banner-jana::after {
        content: "JANA BREITMAR";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: clamp(1rem, 3vw, 1.8rem);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 40px);
        text-align: center;
        padding: 0 20px;
        letter-spacing: clamp(0.5px, 0.3vw, 2px);
        text-shadow:
            0 0 8px rgba(4, 7, 15, 0.8),
            0 0 15px rgba(4, 7, 15, 0.6),
            1px 1px 3px rgba(4, 7, 15, 0.9);
        box-sizing: border-box;
        display: block !important;
    }
}





.impressum-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 80px 20px 40px 20px;
    text-align: center;
    min-height: 100vh;
}

body.impressum-page {
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
    min-height: 100vh;
}

.impressum-content h2 {
    margin: 20px 0 12px 0;
    border-bottom: 2px solid #fbd19a;
    padding-bottom: 8px;
}




.possibilities-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: inline-block; 
    max-width: 600px; 
    margin: 6px auto;
}

.possibilities-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 6px;
    line-height: 1.6;
    text-align: left;
}

.possibilities-list li::before {
    content: "✦";
    position: absolute;
    left: 0;
    top: 0;
    color: #fbd19a; 
    font-size: 16px;
}


.accordion-container {
    margin: 12px auto;
    max-width: 800px;
}

.accordion-item {
    margin-bottom: 6px;
    border-radius: 16px;
    overflow: hidden;
    background: rgba(41, 48, 59, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
}

.accordion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 40px rgba(251, 201, 154, 0.15);
    border-color: rgba(251, 201, 154, 0.3);
}

.accordion-header {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    background: transparent;
    transition: all 0.4s ease;
    user-select: none;
    border: none;
    position: relative;
}

.accordion-header:hover {
    background: rgba(251, 201, 154, 0.05);
}

.accordion-header.active {
    background: rgba(251, 201, 154, 0.08);
}

.day-number {
    font-weight: 400;
    color: #fbc99a;
    min-width: 120px;
    font-size: 16px;
    letter-spacing: 2px;
    text-align: left;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.day-focus {
    flex: 1;
    margin-left: 12px; 
    font-weight: 500;
    color: #ffffff;
    font-size: 15px;
    text-align: left;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.accordion-icon {
    font-size: 18px;
    font-weight: bold;
    color: #fbc99a;
    transition: transform 0.3s ease;
    min-width: 25px;
    text-align: center;
    position: absolute;
    right: 20px; 
    top: 50%;
    transform: translateY(-50%);
}

.accordion-header.active .accordion-icon {
    transform: translateY(-50%) rotate(45deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: rgba(15, 21, 26, 0.8);
    backdrop-filter: blur(5px);
}

.accordion-content.active {
    max-height: 300px;
}

.accordion-content p {
    padding: 16px; 
    margin: 0;
    line-height: 1.7;
    color: #e8e8e8;
    font-size: 15px;
    text-align: left;
    border-top: 1px solid rgba(251, 201, 154, 0.2);
}

.facts-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: inline-block; 
    max-width: 600px; 
    margin: 12px auto; 
}

.facts-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 6px; 
    line-height: 1.6;
    text-align: left;
}

.facts-list li::before {
    content: "▸";
    position: absolute;
    left: 0;
    top: 0;
    color: #04070f;
    font-size: 16px;
    font-weight: bold;
}


.slideshow-container {
    position: relative;
    max-width: 800px;
    margin: 12px auto;
    overflow: hidden;
    border-radius: 12px;
    box-shadow:
        0 8px 32px rgba(251, 201, 154, 0.15),
        0 0 30px rgba(251, 201, 154, 0.1),
        0 0 60px rgba(251, 201, 154, 0.05);
}

.slideshow-wrapper {
    position: relative;
    width: 100%;
    height: 300px; 
    overflow: hidden;
    touch-action: pan-y pinch-zoom;
}

.slides-track {
    display: flex;
    width: 600%; 
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide {
    flex: 0 0 calc(100% / 6); 
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}


.slideshow-nav {
    display: none;
}


.slide-navigation-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 25px;
    margin-top: 40px;
}

.slide-nav-left,
.slide-nav-right {
    position: static;
    width: auto;
    height: auto;
    background: transparent;
    border: none;
    border-radius: 0;
    color: #29303b;
    font-size: 32px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    padding: 12px;
}

.slide-nav-left:hover,
.slide-nav-right:hover {
    color: #1a1f26; 
    transform: scale(1.2); 
}


.slide-indicators-internal {
    position: static; 
    display: flex;
    gap: 8px;
    z-index: 10;
}

.indicator-internal {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(41, 48, 59, 0.3); 
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator-internal.active {
    background: #29303b; 
    transform: scale(1.3);
}

.indicator-internal:hover:not(.active) {
    background: rgba(41, 48, 59, 0.6); 
}


.highlight-box {
    background: transparent; 
    padding: 16px; 
    margin: 0;
    border-radius: 12px;
    box-shadow: none; 
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.highlight-box p {
    margin: 0;
    line-height: 1.6;
    text-align: center; 
    color: #29303b !important; 
    font-size: 16px;
}

.slide-number {
    display: block;
    font-weight: 600;
    color: #29303b;
    font-size: 18px;
    letter-spacing: 3px;
    text-align: center;
    margin-bottom: 16px;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
}

.highlight-box strong {
    color: #29303b !important;
    font-weight: 600;
    display: block;
    margin-bottom: 6px;
    font-size: 18px;
    text-align: center;
}


.facts-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 24px; 
    margin: 16px auto; 
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.facts-box .facts-list {
    margin: 0;
}

.facts-list li {
    color: #04070f;
}

.facts-list li::before {
    color: #04070f;
}


.possibilities-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 24px; 
    margin: 16px auto; 
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.possibilities-box .possibilities-list {
    margin: 0;
}

.possibilities-list li {
    color: #04070f;
}

.possibilities-list li::before {
    color: #04070f;
}


.principles-box {
    background: rgba(251, 201, 154, 0.05);
    border: 2px solid rgba(251, 201, 154, 0.3);
    padding: 24px; 
    margin: 16px 0; 
    border-radius: 12px;
}

.principles-box p {
    margin-bottom: 16px; 
    line-height: 1.7;
}

.principles-box p:last-child {
    margin-bottom: 0;
}


.principles-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px 40px; 
    margin: 32px 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.principle-item {
    text-align: center;
    padding: 0;
}

.principle-item h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #fbd19a;
    margin: 0 0 16px 0;
    line-height: 1.3;
    text-align: center;
}

.principle-item p {
    font-size: 15px;
    line-height: 1.6;
    margin: 0;
    color: #fff8ef;
    text-align: center;
}





.call-to-action-box {
    background: linear-gradient(135deg, rgba(251, 201, 154, 0.15), rgba(251, 201, 154, 0.08));
    border: none;
    padding: 20px; 
    margin: 16px 0; 
    border-radius: 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 25px rgba(251, 201, 154, 0.2);
}


.peach-card {
    background: #fbd19a;
    padding: 20px; 
    margin: 16px auto; 
    border-radius: 15px;
    max-width: 800px;
    box-shadow: 0 8px 25px rgba(251, 209, 154, 0.3);
    text-align: center;
    position: relative;
}

.peach-card p {
    margin-bottom: 16px; 
    line-height: 1.7;
    font-size: 16px;
    color: #38160e;
}

.peach-card p:last-child {
    margin-bottom: 0;
}

.peach-card strong {
    color: #38160e;
    font-weight: 600;
}

.call-to-action-box::before {
    content: "✦";
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    color: #fbd19a; 
    opacity: 0.7;
}

.call-to-action-box p {
    margin-bottom: 16px; 
    line-height: 1.8;
    font-size: 16px;
}

.call-to-action-box p:last-child {
    margin-bottom: 0;
}

.call-emphasis {
    font-size: 20px !important;
    color: #fbd19a !important; 
    margin: 24px 0 !important; 
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600 !important;
    letter-spacing: 1px;
}

.call-final {
    font-style: italic !important;
    font-size: 17px !important;
    margin-top: 24px !important; 
    color: #fbd19a !important; 
}


.community-section {
    background-color: #04070f; 
    padding: 24px 20px; 
    margin: 0;
    width: 100%;
}



.community-content {
    max-width: 800px;
    margin: 0 auto;
    color: #fff8ef;
    line-height: 1.8;
    text-align: center;
}

.community-content p {
    margin-bottom: 12px; 
    font-size: 16px;
    color: #fff8ef;
}

.letter-body {
    margin-bottom: 16px; 
}

.letter-body p {
    margin-bottom: 12px; 
    line-height: 1.8;
    color: #fff8ef;
}

.letter-action {
    text-align: center;
    margin-top: 16px; 
}


.letter-greeting {
    margin-bottom: 16px; 
}

.letter-greeting p {
    font-size: 18px;
    font-style: italic;
    color: #fff8ef;
    font-weight: 500;
}

.letter-vision {
    margin: 24px 0; 
}

.letter-vision p {
    margin-bottom: 16px; 
    line-height: 1.7;
}

.letter-emphasis {
    font-weight: 600;
    color: #fff8ef;
}

.letter-signature {
    margin: 24px 0; 
    text-align: center;
    font-style: italic;
    color: #fff8ef;
}












.social-links .einheitsbutton {
    margin: 0 !important;
    display: inline-block !important;
    width: auto !important;
    flex-shrink: 0;
}


.letter-closing-banner {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
    position: relative;
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}



.letter-closing-banner .closing-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 40px;
    align-items: center;
    max-width: 800px;
    width: 100%;
}

.letter-closing-banner .closing-profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    justify-self: center;
}

.letter-closing-banner .closing-profile-image:hover {
    transform: scale(1.05);
}


.image-zoom-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    cursor: pointer;
}

.image-zoom-modal.active {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.image-zoom-content {
    max-width: 90%;
    max-height: 90%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: zoomIn 0.3s ease;
}

.image-zoom-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: #fbc99a;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    z-index: 10000;
}

.image-zoom-close:hover {
    color: #f59034;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from {
        transform: scale(0.5);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}


@media (max-width: 768px) {
    .image-zoom-close {
        top: 10px;
        right: 15px;
        font-size: 30px;
    }

    .image-zoom-content {
        max-width: 95%;
        max-height: 95%;
    }
}

.letter-closing-banner .closing-text {
    text-align: center;
}

.letter-closing-banner .closing-text .letter-signature {
    font-size: 28px;
    font-style: italic;
    color: #fbd19a; 
    margin-bottom: 16px; 
    font-weight: 300;
}

.letter-closing-banner .closing-text h3 {
    color: #fbd19a; 
    font-size: 22px;
    margin: 0 0 25px 0;
    font-weight: 600;
}


.letter-closing-banner .about-me-link {
    display: inline-block;
    color: #8b6c5f; 
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    margin: 10px 0 15px 0;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.letter-closing-banner .about-me-link:hover {
    color: #fbd19a; 
    opacity: 1;
    text-decoration: underline;
}

.letter-closing-banner .closing-text .social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}


.letter-closing-banner .einheitsbutton.instagram {
    background-color: transparent !important;
    border: 2px solid #e91e63 !important;
    color: #e91e63 !important;
}

.letter-closing-banner .einheitsbutton.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
    border: 2px solid transparent !important;
    color: white !important;
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.spotify {
    background-color: transparent !important;
    border-color: #1DB954 !important;
    color: #1DB954 !important;
}

.letter-closing-banner .einheitsbutton.spotify:hover {
    background-color: #1DB954 !important;
    border-color: #1DB954 !important;
    color: #04070f !important; 
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.telegram {
    background-color: transparent !important;
    border-color: #0088cc !important;
    color: #0088cc !important;
}

.letter-closing-banner .einheitsbutton.telegram:hover {
    background-color: #0088cc !important;
    border-color: #0088cc !important;
    color: #04070f !important; 
    transform: scale(1.05);
}


.einheitsbutton {
    display: block;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: auto;
    width: fit-content;
    text-align: center;
    background-color: #fbd19a;
    color: #29303b;
    border: 2px solid #fbd19a;
}


.einheitsbutton:hover {
    background-color: #04070f; 
    color: #fbd19a; 
    border-color: #fbd19a;
    transform: scale(1.05);
}


.einheitsbutton.pink-button {
    background-color: #fbc99a;
    color: #29303b;
    border: 2px solid #fbc99a;
    opacity: 1;
    pointer-events: none;
    cursor: not-allowed;
}

.einheitsbutton.pink-button:hover {
    background-color: #fbc99a;
    color: #29303b;
    border: 2px solid #fbc99a;
    transform: none;
}


.einheitsbutton.transparent {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.einheitsbutton.transparent:hover {
    background-color: white;
    color: #04070f; 
    border-color: white;
    transform: scale(1.05);
}


.events-text {
    max-width: 800px;
    margin: 20px auto 12px auto; 
    padding: 0 30px;
    text-align: center;
    line-height: 1.8;
}

.events-text p {
    margin-bottom: 12px; 
    font-size: 16px;
}


.events-grid-container {
    max-width: 1200px;
    margin: 12px auto 20px auto; 
    padding: 0 30px;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 100%;
}


@media (min-width: 1020px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 380px));
    }
}


@media (min-width: 1400px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 420px));
    }
}

.event-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    will-change: auto;
}

.event-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}


.event-description {
    font-size: 14px;
    margin-bottom: 15px;
    color: #fff8ef; 
    flex-grow: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 18px;
    margin-top: 25px;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
}


.einheitsbutton.instagram {
    border: 2px solid transparent;
    background: linear-gradient(#04070f, #04070f) padding-box, 
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: #e6683c;
}

.einheitsbutton.instagram:hover {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: white;
    transform: scale(1.05);
}


.einheitsbutton.youtube {
    background-color: #04070f; 
    border-color: #FF0000;
    color: #FF0000;
}

.einheitsbutton.youtube:hover {
    background-color: #FF0000;
    border-color: #FF0000;
    color: #04070f; 
    transform: scale(1.05);
}


.einheitsbutton.spotify {
    background-color: #04070f; 
    border-color: #1DB954;
    color: #1DB954;
}

.einheitsbutton.spotify:hover {
    background-color: #1DB954;
    border-color: #1DB954;
    color: #04070f; 
    transform: scale(1.05);
}


.einheitsbutton.telegram {
    background-color: #04070f; 
    border-color: #0088cc;
    color: #0088cc;
}

.einheitsbutton.telegram:hover {
    background-color: #0088cc;
    border-color: #0088cc;
    color: #04070f; 
    transform: scale(1.05);
}


.events-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.events-horizontal-image {
    width: 100%;
    max-width: 1120px; 
    height: 360px; 
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 16px; 
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.events-title {
    color: #fbd19a !important;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 800px;
    margin: 0 20px;
    text-shadow: none;
}


.events-content-section {
    background-color: #fff8ef;
    padding: 24px 20px; 
    margin: 0;
    width: 100%;
    text-align: center; 
}

.events-content-section p {
    max-width: 800px;
    margin: 0 auto 12px auto; 
    color: #04070f;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.events-content-section h3 {
    max-width: 800px;
    margin: 16px auto 8px auto; 
    color: #04070f;
    text-align: center;
}

.events-content-section h3:first-child {
    margin-top: 0;
}

.events-content-section strong {
    color: #04070f;
}


.events-content-section .possibilities-list,
.events-content-section .facts-list {
    display: inline-block;
    text-align: left;
    max-width: 600px;
    margin: 16px auto; 
}

.events-content-section .possibilities-list li,
.events-content-section .facts-list li {
    color: #04070f;
}

.events-content-section .possibilities-list li::before {
    color: #fbd19a; 
}

.events-content-section .facts-list li::before {
    color: #04070f; 
}


.zwischenmenschlichkeit-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #fff8ef 50%, #04070f 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.zwischenmenschlichkeit-horizontal-image {
    width: 100%;
    max-width: 1120px; 
    height: 360px; 
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 16px; 
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}




.zwischenmenschlichkeit-content-section {
    background-color: #04070f;
    padding: 16px 20px 24px 20px;
    margin: 0;
    width: 100%;
}

.zwischenmenschlichkeit-content-section.event-section-white {
    background-color: #fff8ef;
}

.zwischenmenschlichkeit-content-section p {
    max-width: 800px;
    margin: 0 auto 12px auto;
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.zwischenmenschlichkeit-content-section.event-section-white p {
    color: #04070f;
}

.zwischenmenschlichkeit-content-section h3 {
    max-width: 800px;
    margin: 16px auto 8px auto;
    color: #fbd19a;
    text-align: center;
}

.zwischenmenschlichkeit-content-section.event-section-white h3 {
    color: #04070f;
}

.zwischenmenschlichkeit-content-section h2,
.zwischenmenschlichkeit-content-section h3 {
    text-align: center;
}

.zwischenmenschlichkeit-content-section h2:first-child,
.zwischenmenschlichkeit-content-section h3:first-child {
    margin-top: 0;
}

.zwischenmenschlichkeit-content-section strong {
    color: #fbd19a;
}

.zwischenmenschlichkeit-content-section.event-section-white strong {
    color: #04070f;
}




.community-button-section {
    text-align: center;
    margin-top: 20px; 
    padding: 16px 0; 
}

.community-button-section .einheitsbutton {
    display: inline-block;
    margin: 0 auto;
}


.highlights-box {
    background-color: #f8f9fa;
    border-left: 4px solid #fbd19a; 
    padding: 16px; 
    margin: 16px 0; 
    border-radius: 5px;
}

.signature {
    text-align: right;
    margin-top: 20px; 
    font-style: italic;
    color: #fbd19a; 
    font-size: 18px;
}

.signature a {
    color: #fbd19a; 
    text-decoration: none;
    font-style: italic;
}

.signature a:hover {
    color: #fbd19a; 
    text-decoration: none;
}











* {
    -webkit-tap-highlight-color: transparent;
}


::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: #29303b;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(251, 209, 154, 0.6); 
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(251, 209, 154, 0.8); 
}


@media (hover: none) and (pointer: coarse) {
    .einheitsbutton:hover,
    .event-card:hover {
        transform: none !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        background-color: inherit !important;
    }
}


@media (max-width: 768px) {
    
    html, body {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    
    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 24px;
    }

    h3 {
        font-size: 16px;
    }

    h4 {
        font-size: 14px;
    }

    .banner h2 {
        font-size: 1.8rem;
    }



    
    .community-section {
        padding: 24px 15px; 
    }



    .community-content {
        padding: 0 8px; 
    }

    
    .events-image-section {
        min-height: 350px;
        padding: 12px 15px; 
    }

    .events-horizontal-image {
        height: 240px; 
        margin-bottom: 12px; 
    }

    .events-title {
        font-size: 1.6rem;
        padding: 12px 20px; 
        margin: 0 10px;
        line-height: 1.3;
    }

    .events-content-section {
        padding: 24px 15px; 
    }

    
    .slideshow-container {
        margin: 12px auto; 
        border-radius: 8px;
    }

    .slideshow-wrapper {
        height: 280px;
    }

    .slide-navigation-container {
        gap: 20px;
        margin-top: 35px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: auto;
        height: auto;
        font-size: 28px;
        background: transparent;
        border: none;
        color: #29303b;
        padding: 10px;
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        color: #1a1f26;
        transform: scale(1.2);
    }

    .slide-indicators-internal {
        gap: 6px;
    }

    .indicator-internal {
        width: 5px;
        height: 5px;
        background: rgba(41, 48, 59, 0.3); 
    }

    .indicator-internal.active {
        background: #29303b; 
    }

    .indicator-internal:hover:not(.active) {
        background: rgba(41, 48, 59, 0.6); 
    }

    .highlight-box {
        padding: 16px; 
        border-radius: 8px;
        background: transparent; 
        box-shadow: none; 
    }

    .slide-number {
        font-size: 17px;
        font-weight: 600;
        letter-spacing: 2.5px;
        margin-bottom: 14px;
        text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
    }

    .highlight-box strong {
        font-size: 16px;
        margin-bottom: 6px;
        color: #29303b !important;
        text-align: center;
    }

    .highlight-box p {
        font-size: 15px;
        color: #29303b !important; 
        text-align: center; 
    }

    
    .zwischenmenschlichkeit-image-section {
        min-height: 350px;
        padding: 16px 15px; 
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 240px; 
        margin-bottom: 16px; 
    }



    .zwischenmenschlichkeit-content-section {
        padding: 16px 15px 32px 15px; 
    }

    .events-text {
        margin: 20px;
        padding: 0 20px;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .banner {
        height: 300px;
    }

    /* Hero Section Mobile Styles */
    .hero-section {
        height: 100vh;
        min-height: 100vh;
        max-height: 100vh;
        padding: 0;
        margin: 0;
        overflow: hidden;
    }

    .hero-brand {
        top: 20px;
        left: 20px;
        font-size: 22px;
        font-weight: 300;
        letter-spacing: 1px;
        height: 25px;
        line-height: 25px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        height: 100vh;
        gap: 0;
        padding: 0;
        margin: 0;
    }

    .hero-text {
        align-items: center;
        text-align: center;
        order: 1;
        height: 50vh;
        padding: 80px 20px 20px 20px;
        margin: 0;
    }

    .hero-image {
        order: 2;
        height: 50vh;
        margin: 0;
    }

    .hero-title {
        font-size: 2.5rem;
        text-align: center;
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: 22px;
        letter-spacing: 2px;
        margin-bottom: 20px;
        text-align: center;
    }

    .hero-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0;
    }

    
    .banner.banner-zusammenhalt {
        background-size: 200%;
        background-position: center 55%;
    }

    
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-zukunftinsjetzt::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }





    
    .banner-zukunftinsjetzt::after {
        content: "CO-KREATION FÜR LIGHTWORKER";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-size: 1.1rem;
        font-weight: 400;
        text-align: center;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    
    .banner-event {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-event::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    




    
    .banner-jana {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-jana::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    
    .banner-jana::after {
        content: "JANA BREITMAR";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: 1rem;
        top: 53%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    
    .letter-closing-banner {
        height: 450px; 
    }

    

    .letter-closing-banner {
        padding: 40px 20px; 
    }

    .letter-closing-banner .closing-content {
        grid-template-columns: 1fr;
        gap: 30px; 
        text-align: center;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 140px;
        height: 140px;
        justify-self: center;
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 18px;
        margin-bottom: 20px;
    }

    
    .letter-closing-banner .about-me-link {
        font-size: 12px;
        margin: 8px 0 12px 0;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 8px;
        margin-bottom: 20px; 
    }

    .social-links {
        flex-direction: row;
        align-items: flex-start;
        gap: 18px;
    }

    .social-links .einheitsbutton {
        font-size: 12px !important;
        padding: 8px 15px !important;
        flex-shrink: 1;
        min-width: 0;
    }

    .highlights-box,
    .details-box,
    .participation-box {
        margin: 20px 0;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    
    h1 {
        font-size: 1.5rem;
    }

    h2 {
        font-size: 20px;
    }

    h3 {
        font-size: 14px;
    }

    h4 {
        font-size: 13px;
    }

    .banner h2 {
        font-size: 1.5rem;
    }



    
    .community-section {
        padding: 24px 10px; 
    }



    
    .events-image-section {
        min-height: 300px;
        padding: 16px 10px; 
    }

    .events-horizontal-image {
        height: 180px; 
        margin-bottom: 16px; 
    }

    .events-title {
        font-size: 1.3rem;
        padding: 16px; 
        margin: 0 8px; 
        line-height: 1.2;
    }

    .events-content-section {
        padding: 24px 10px; 
    }

    
    .slideshow-container {
        margin: 10px auto;
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 260px;
    }

    .slide-navigation-container {
        gap: 18px;
        margin-top: 30px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: auto;
        height: auto;
        font-size: 24px;
        border: none;
        padding: 8px;
    }

    .slide-indicators-internal {
        gap: 5px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
    }

    .highlight-box {
        padding: 20px;
        border-radius: 6px;
        background: transparent; 
        box-shadow: none; 
    }

    .slide-number {
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 2px;
        margin-bottom: 12px;
        text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
    }

    .highlight-box strong {
        font-size: 15px;
        margin-bottom: 8px;
        color: #29303b !important;
        text-align: center;
    }

    .highlight-box p {
        font-size: 14px;
        line-height: 1.5;
        color: #29303b !important; 
        text-align: center; 
    }

    
    .zwischenmenschlichkeit-image-section {
        min-height: 300px;
        padding: 15px 10px;
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 180px; 
        margin-bottom: 15px;
    }



    .zwischenmenschlichkeit-content-section {
        padding: 15px 10px 30px 10px;
    }

    .events-text {
        margin: 10px;
        padding: 0 15px;
    }

    .banner {
        height: 250px;
    }

    /* Hero Section Small Mobile Styles */
    .hero-brand {
        top: 15px;
        left: 15px;
        font-size: 18px;
        font-weight: 300;
        letter-spacing: 1px;
        height: 22px;
        line-height: 22px;
    }

    .hero-text {
        padding: 70px 15px 15px 15px;
    }

    .hero-title {
        font-size: 2rem;
        margin-bottom: 12px;
    }

    .hero-subtitle {
        font-size: 18px;
        letter-spacing: 1.5px;
        margin-bottom: 18px;
    }

    
    .banner.banner-zusammenhalt {
        background-size: 220%;
        background-position: center 50%;
    }

    
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }



    .banner-zukunftinsjetzt::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    
    .banner-event {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    
    .scroll-indicator {
        bottom: 20px;
    }

    .arrow-symbol {
        font-size: 24px;
    }

    .banner-event::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    
    .banner-jana {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    .banner-jana::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 12px !important;
    }

    
    .letter-closing-banner {
        height: 400px; 
    }

    

    .letter-closing-banner {
        padding: 40px 15px; 
    }

    .letter-closing-banner .closing-content {
        gap: 25px;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 110px;
        height: 110px;
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 16px;
        margin-bottom: 15px;
    }

    
    .letter-closing-banner .about-me-link {
        font-size: 11px;
        margin: 6px 0 10px 0;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 6px;
        margin-bottom: 15px; 
    }

    .letter-closing-banner .closing-text .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 10px !important; 
    }

    
    .principles-grid {
        grid-template-columns: 1fr;
        gap: 24px;
        margin: 24px 0;
        max-width: 100%;
        padding: 0 16px;
    }

    .principle-item h4 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .principle-item p {
        font-size: 14px;
        line-height: 1.5;
    }
}


@media (min-width: 769px) and (max-width: 1024px) {
    .principles-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 28px 36px;
        margin: 28px 0;
        padding: 0 24px;
    }

    .principle-item h4 {
        font-size: 17px;
    }

    .principle-item p {
        font-size: 14px;
    }
}


.about-section {
    padding: 24px 20px;
    margin: 0;
    width: 100%;
    text-align: center;
    position: relative;
}

.about-section-white {
    background-color: #fff8ef;
}

.about-section-black {
    background-color: #04070f !important;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-content p {
    margin-bottom: 12px;
}

.about-section-white .about-content p {
    color: #04070f;
    font-size: 16px;
    line-height: 1.8;
}

.about-section-black .about-content p {
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
}

.about-section-white .about-content strong {
    color: #04070f;
}

.about-section-black .about-content strong {
    color: #fbc99a;
}

.about-section-white .about-content a {
    color: #04070f;
    text-decoration: underline;
}

.about-section-black .about-content a {
    color: #fbc99a;
    text-decoration: underline;
}


.background-list-content {
    display: flex;
    justify-content: center;
}

.background-facts-list {
    list-style: none;
    padding: 0 10px;
    margin: 0;
    text-align: left;
    max-width: 800px;
}

.background-facts-list li {
    margin-bottom: 12px;
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
}

.background-facts-list li strong {
    color: #fbc99a;
}





.event-section {
    padding: 24px 20px; 
    margin: 0;
    width: 100%;
    text-align: center;
    position: relative;
}

.event-section-white {
    background-color: #fff8ef;
}

.event-section-black {
    background-color: #04070f;
}

.event-section-pink {
    background-color: #870833;
}

.event-section-blue-gradient {
    background: #04070f;
}

.event-section-blue-gradient.prefooter {
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
}

.button-container {
    margin-top: 30px;
}

.profile-image-container {
    text-align: center;
    margin: 15px 0;
}

.profile-image-round {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    box-shadow: 0 8px 32px rgba(251, 201, 154, 0.3);
}

.vision-text {
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
}

.facts-container {
    max-width: 600px;
    margin: 20px auto;
}

.instagram-link {
    color: #fbc99a;
    text-decoration: underline;
}

.signature-text {
    text-align: center;
    margin-top: 15px;
    font-size: 18px;
    font-style: italic;
    color: #fff8ef;
    font-weight: 500;
}

.final-banner {
    background-image: url('assets/bilder/zusammenhalt.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0;
    padding: 0;
}

.final-banner-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.final-banner-text {
    color: #fbc99a;
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow:
        0 0 10px rgba(4, 7, 15, 0.8),
        0 0 20px rgba(4, 7, 15, 0.6),
        2px 2px 4px rgba(4, 7, 15, 0.9);
    font-family: 'Montserrat', sans-serif;
}


.transition-image {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    z-index: 10;
}





.event-content {
    max-width: 800px;
    margin: 0 auto;
}

.event-content p {
    margin-bottom: 12px; 
}


.event-transition-container {
    position: relative;
    width: 100%;
    height: 360px; 
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.event-transition-image {
    width: 100%;
    max-width: 1120px; 
    height: 360px; 
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 10;
    position: relative;
}


.event-section-white + .event-transition-container,
.about-section-white + .event-transition-container {
    background: linear-gradient(to bottom, #fff8ef 50%, #04070f 50%);
}


.event-section-black + .event-transition-container,
.about-section-black + .event-transition-container {
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
}


.event-transition-to-pink {
    background: linear-gradient(to bottom, #fff8ef 50%, #870833 50%) !important;
}


.zwischenmenschlichkeit-content-section ~ .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}


.event-section-black + .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}


.about-section-white + .event-transition-to-pink {
    background: linear-gradient(to bottom, #fff8ef 50%, #870833 50%) !important;
}


.about-section-black + .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}



.event-section-white .event-content p {
    color: #04070f;
}

.event-section-black .event-content p {
    color: #fff8ef;
}

.event-section-pink .event-content p {
    color: #fff8ef;
}

.event-section-blue-gradient .event-content p {
    color: #fff8ef;
}

.event-section-white .event-content strong {
    color: #04070f;
}

.event-section-black .event-content strong {
    color: #fff8ef;
}

.event-section-pink .event-content strong {
    color: #fff8ef;
}

.event-section-blue-gradient .event-content strong {
    color: #fff8ef;
}


.event-section .possibilities-list,
.event-section .facts-list {
    display: inline-block;
    text-align: left;
    max-width: 600px;
    margin: 6px auto; 
}

.event-section-white .possibilities-list li,
.event-section-white .facts-list li {
    color: #04070f;
}

.event-section-black .possibilities-list li,
.event-section-black .facts-list li {
    color: #fff8ef;
}

.event-section-white .possibilities-list li::before {
    color: #fbc99a; 
}

.event-section-black .possibilities-list li::before {
    color: #fbc99a; 
}

.event-section-white .facts-list li::before {
    color: #04070f; 
}

.event-section-black .facts-list li::before {
    color: #fff8ef;
}

.event-section-blue-gradient .facts-list li {
    color: #fff8ef;
}

.event-section-blue-gradient .facts-list li::before {
    color: #fbc99a;
}

.event-section-blue-gradient h3 {
    color: #fff8ef;
}


.event-section-white .accordion-container,
.event-section-black .accordion-container {
    background: transparent;
}


.event-section-white .accordion-item,
.event-section-black .accordion-item {
    background: rgba(255, 248, 239, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(41, 48, 59, 0.2);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.15),
        0 0 30px rgba(251, 201, 154, 0.3),
        0 0 60px rgba(251, 201, 154, 0.1);
}

.event-section-white .accordion-item:hover,
.event-section-black .accordion-item:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 40px rgba(41, 48, 59, 0.25),
        0 0 40px rgba(251, 201, 154, 0.5),
        0 0 80px rgba(251, 201, 154, 0.2);
    border-color: rgba(41, 48, 59, 0.4);
}

.event-section-white .accordion-header,
.event-section-black .accordion-header {
    background: rgba(251, 201, 154, 0.06);
}

.event-section-white .accordion-header:hover,
.event-section-black .accordion-header:hover {
    background: rgba(251, 201, 154, 0.12);
}

.event-section-white .accordion-header.active,
.event-section-black .accordion-header.active {
    background: rgba(251, 201, 154, 0.18);
}

.event-section-white .day-number,
.event-section-black .day-number {
    color: #29303b;
    text-shadow: none;
    font-weight: 600;
}

.event-section-white .day-focus,
.event-section-black .day-focus {
    color: #29303b;
    text-shadow: none;
    font-weight: 500;
}

.event-section-white .accordion-icon,
.event-section-black .accordion-icon {
    color: #29303b;
    min-width: 25px;
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    font-weight: 600;
}

.event-section-white .accordion-header.active .accordion-icon,
.event-section-black .accordion-header.active .accordion-icon {
    transform: translateY(-50%) rotate(45deg);
}

.event-section-white .accordion-content,
.event-section-black .accordion-content {
    background: rgba(255, 248, 239, 0.98);
    backdrop-filter: blur(5px);
}

.event-section-white .accordion-content p,
.event-section-black .accordion-content p {
    color: #29303b;
    border-top: 1px solid rgba(41, 48, 59, 0.2);
}


.event-section-black .peach-card {
    background-color: rgba(251, 217, 154, 0.15);
    border: 1px solid rgba(251, 217, 154, 0.3);
    color: #fff8ef;
}


@media (max-width: 768px) {
    .about-section {
        padding: 24px 15px;
    }
}


@media (max-width: 768px) {
    .event-section {
        padding: 24px 15px; 
    }



    .transition-image {
        width: 60px;
        height: 60px;
        top: -30px;
    }

    .event-transition-container {
        height: 240px; 
    }

    .event-transition-image {
        height: 240px; 
        max-width: 85%; 
    }

    
    .slideshow-container {
        margin: 12px 8px; 
        max-width: calc(100vw - 32px);
        border-radius: 8px;
    }

    .slideshow-wrapper {
        height: 320px; 
    }

    .highlight-box {
        padding: 16px; 
        border-radius: 8px;
        font-size: 14px;
        background: transparent; 
        box-shadow: none; 
    }

    .highlight-box p {
        line-height: 1.6;
        color: #29303b !important; 
        text-align: center; 
    }

    .slide-number {
        font-size: 15px;
        font-weight: 600;
        letter-spacing: 2px;
        margin-bottom: 10px;
        text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
    }

    .highlight-box strong {
        color: #29303b !important;
        text-align: center;
    }

    .slide-navigation-container {
        gap: 20px;
        margin-top: 35px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: auto;
        height: auto;
        font-size: 28px;
        background: transparent;
        border: none;
        color: #29303b;
        padding: 10px;
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        color: #1a1f26;
        transform: scale(1.2);
    }

    .slide-indicators-internal {
        gap: 6px;
    }

    
    .accordion-container {
        margin: 12px 8px; 
        max-width: calc(100vw - 32px);
    }

    .accordion-header {
        padding: 12px; 
        flex-wrap: wrap;
    }

    .day-number {
        min-width: auto;
        font-size: 14px;
        letter-spacing: 1px;
        margin-bottom: 6px; 
        width: 100%;
    }

    .day-focus {
        margin-left: 0;
        font-size: 14px;
        flex: 1;
    }

    .accordion-icon {
        font-size: 16px;
        min-width: 20px;
    }

    .accordion-content p {
        padding: 12px; 
        font-size: 14px;
        line-height: 1.6;
    }

    .accordion-content.active {
        max-height: 300px; 
    }


}

@media (max-width: 480px) {
    .about-section {
        padding: 20px 10px;
    }

    .event-section {
        padding: 20px 10px; 
    }



    .transition-image {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    .event-transition-container {
        height: 180px; 
    }

    .event-transition-image {
        height: 180px; 
        max-width: 80%; 
    }

    
    .slideshow-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 300px; 
    }

    .highlight-box {
        padding: 15px 10px;
        border-radius: 6px;
        font-size: 13px;
        background: transparent; 
        box-shadow: none; 
    }

    .slide-number {
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 1.5px;
        margin-bottom: 8px;
        text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
    }

    .highlight-box strong {
        font-size: 14px;
        color: #29303b !important;
        text-align: center;
    }

    .highlight-box p {
        color: #29303b !important; 
        text-align: center; 
    }

    .slide-navigation-container {
        gap: 15px;
        margin-top: 30px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: auto;
        height: auto;
        font-size: 22px;
        background: transparent;
        border: none;
        color: #29303b;
        padding: 8px;
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        color: #1a1f26;
        transform: scale(1.2);
    }

    .slide-indicators-internal {
        gap: 5px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
        background: rgba(41, 48, 59, 0.3); 
    }

    .indicator-internal.active {
        background: #29303b; 
    }

    .indicator-internal:hover:not(.active) {
        background: rgba(41, 48, 59, 0.6); 
    }

    
    .accordion-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
    }

    .accordion-header {
        padding: 12px 8px;
    }

    .day-number {
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .day-focus {
        font-size: 13px;
    }

    .accordion-icon {
        font-size: 14px;
        min-width: 18px;
    }

    .accordion-content p {
        padding: 12px 8px;
        font-size: 13px;
        line-height: 1.5;
    }

    .accordion-content.active {
        max-height: 350px;
    }
}

@media (max-width: 480px) {
    .about-section {
        padding: 20px 10px;
    }

    .event-section {
        padding: 20px 10px;
    }



    .transition-image {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    .event-transition-container {
        height: 180px;
    }

    .event-transition-image {
        height: 180px;
        max-width: 80%;
    }


    .slideshow-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 300px;
    }

    .highlight-box {
        padding: 15px 10px;
        border-radius: 6px;
        font-size: 13px;
        background: transparent;
        box-shadow: none;
    }

    .slide-number {
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 1.5px;
        margin-bottom: 8px;
        text-shadow: 0 1px 2px rgba(41, 48, 59, 0.1);
    }

    .highlight-box strong {
        font-size: 14px;
        color: #29303b !important;
        text-align: center;
    }

    .highlight-box p {
        color: #29303b !important;
        text-align: center;
    }

    .slide-navigation-container {
        gap: 15px;
        margin-top: 30px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: auto;
        height: auto;
        font-size: 22px;
        background: transparent;
        border: none;
        color: #29303b;
        padding: 8px;
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        color: #1a1f26;
        transform: scale(1.2);
    }

    .slide-indicators-internal {
        gap: 5px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
        background: rgba(41, 48, 59, 0.3);
    }

    .indicator-internal.active {
        background: #29303b;
    }

    .indicator-internal:hover:not(.active) {
        background: rgba(41, 48, 59, 0.6);
    }


    .accordion-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
    }

    .accordion-header {
        padding: 12px 8px;
    }

    .day-number {
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .day-focus {
        font-size: 13px;
    }

    .accordion-icon {
        font-size: 14px;
        min-width: 18px;
    }

    .accordion-content p {
        padding: 12px 8px;
        font-size: 13px;
        line-height: 1.5;
    }

    .accordion-content.active {
        max-height: 350px;
    }


}